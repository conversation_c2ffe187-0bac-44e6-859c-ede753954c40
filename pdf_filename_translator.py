#!/usr/bin/env python3
"""
PDF Filename Translator

This program reads API configuration from config.txt, scans a PDF folder,
translates PDF filenames to Chinese using an AI API, and creates copies
with translated filenames.

Author: AI Assistant
Date: 2025-07-31
"""

import os
import re
import shutil
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import requests
import json
import time
from urllib.parse import urljoin


class ConfigParser:
    """Parse configuration from config.txt file."""
    
    def __init__(self, config_file: str = "config.txt"):
        self.config_file = config_file
        self.config = {}
        self.load_config()
    
    def load_config(self) -> None:
        """Load configuration from file."""
        try:
            if not os.path.exists(self.config_file):
                raise FileNotFoundError(f"Configuration file {self.config_file} not found")
            
            with open(self.config_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if line and ':' in line:
                        key, value = line.split(':', 1)
                        self.config[key.strip()] = value.strip()
                    elif line and not line.startswith('#'):
                        logging.warning(f"Invalid config line {line_num}: {line}")
            
            # Validate required configuration
            required_keys = ['base_url', 'api', 'model']
            missing_keys = [key for key in required_keys if key not in self.config]
            if missing_keys:
                raise ValueError(f"Missing required configuration keys: {missing_keys}")
                
            logging.info(f"Configuration loaded successfully from {self.config_file}")
            
        except Exception as e:
            logging.error(f"Failed to load configuration: {e}")
            raise
    
    def get(self, key: str, default: Optional[str] = None) -> Optional[str]:
        """Get configuration value."""
        return self.config.get(key, default)
    
    def get_all(self) -> Dict[str, str]:
        """Get all configuration values."""
        return self.config.copy()


class AITranslator:
    """Handle AI API calls for translation."""
    
    def __init__(self, config: ConfigParser):
        self.base_url = config.get('base_url')
        self.api_key = config.get('api')
        self.model = config.get('model')
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        })
        
        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 0.5  # 500ms between requests
        
        logging.info(f"AI Translator initialized with model: {self.model}")
    
    def _wait_for_rate_limit(self) -> None:
        """Ensure we don't exceed rate limits."""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        if time_since_last < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last
            time.sleep(sleep_time)
        self.last_request_time = time.time()
    
    def translate_filename(self, filename: str, max_retries: int = 3) -> Optional[str]:
        """
        Translate a filename to Chinese using the AI API.
        
        Args:
            filename: The filename to translate (without extension)
            max_retries: Maximum number of retry attempts
            
        Returns:
            Translated filename or None if translation fails
        """
        self._wait_for_rate_limit()
        
        # Create translation prompt
        prompt = f"""Please translate the following PDF filename to Chinese. 
The filename should be concise, accurate, and suitable for use as a file name.
Only return the translated Chinese filename, nothing else.

Filename to translate: {filename}"""

        payload = {
            "model": self.model,
            "messages": [
                {
                    "role": "system",
                    "content": "You are a professional translator. Translate the given filename to Chinese, ensuring it's suitable for use as a filename (no special characters that are invalid for filenames)."
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "max_tokens": 100,
            "temperature": 0.3
        }
        
        for attempt in range(max_retries):
            try:
                # Construct the full URL for chat completions
                url = urljoin(self.base_url.rstrip('/') + '/', 'chat/completions')
                
                response = self.session.post(url, json=payload, timeout=30)
                response.raise_for_status()
                
                result = response.json()
                
                if 'choices' in result and len(result['choices']) > 0:
                    translated = result['choices'][0]['message']['content'].strip()
                    # Clean the translated filename
                    cleaned = self._clean_filename(translated)
                    if cleaned:
                        logging.info(f"Translated '{filename}' -> '{cleaned}'")
                        return cleaned
                    else:
                        logging.warning(f"Translation result is empty after cleaning: '{translated}'")
                else:
                    logging.error(f"Unexpected API response format: {result}")
                    
            except requests.exceptions.RequestException as e:
                logging.error(f"API request failed (attempt {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)  # Exponential backoff
            except json.JSONDecodeError as e:
                logging.error(f"Failed to parse API response (attempt {attempt + 1}/{max_retries}): {e}")
            except Exception as e:
                logging.error(f"Unexpected error during translation (attempt {attempt + 1}/{max_retries}): {e}")
        
        logging.error(f"Failed to translate filename after {max_retries} attempts: {filename}")
        return None
    
    def _clean_filename(self, filename: str) -> str:
        """
        Clean filename to ensure it's valid for the file system.
        
        Args:
            filename: Raw filename from translation
            
        Returns:
            Cleaned filename safe for file system use
        """
        # Remove quotes and extra whitespace
        cleaned = filename.strip().strip('"\'')
        
        # Replace invalid characters for Windows/Unix file systems
        invalid_chars = r'[<>:"/\\|?*]'
        cleaned = re.sub(invalid_chars, '_', cleaned)
        
        # Remove control characters
        cleaned = re.sub(r'[\x00-\x1f\x7f]', '', cleaned)
        
        # Limit length (Windows has 255 char limit, leave room for extension)
        if len(cleaned) > 200:
            cleaned = cleaned[:200].strip()
        
        # Ensure it's not empty and doesn't end with period or space
        cleaned = cleaned.strip('. ')
        
        return cleaned if cleaned else "translated_file"


class PDFProcessor:
    """Handle PDF file operations."""
    
    def __init__(self, pdf_folder: str = "pdf", output_folder: str = "translated_pdfs"):
        self.pdf_folder = Path(pdf_folder)
        self.output_folder = Path(output_folder)
        self.translator = None
        
        # Ensure folders exist
        if not self.pdf_folder.exists():
            raise FileNotFoundError(f"PDF folder not found: {self.pdf_folder}")
        
        self.output_folder.mkdir(exist_ok=True)
        logging.info(f"PDF Processor initialized - Input: {self.pdf_folder}, Output: {self.output_folder}")
    
    def set_translator(self, translator: AITranslator) -> None:
        """Set the AI translator instance."""
        self.translator = translator
    
    def scan_pdf_files(self) -> List[Path]:
        """
        Scan the PDF folder for all PDF files.
        
        Returns:
            List of PDF file paths
        """
        pdf_files = list(self.pdf_folder.glob("*.pdf"))
        pdf_files.extend(self.pdf_folder.glob("*.PDF"))  # Case insensitive
        
        logging.info(f"Found {len(pdf_files)} PDF files in {self.pdf_folder}")
        return sorted(pdf_files)
    
    def process_files(self, pdf_files: List[Path]) -> Dict[str, str]:
        """
        Process PDF files by translating filenames and creating copies.
        
        Args:
            pdf_files: List of PDF file paths to process
            
        Returns:
            Dictionary mapping original filenames to translated filenames
        """
        if not self.translator:
            raise ValueError("Translator not set. Call set_translator() first.")
        
        results = {}
        successful_translations = 0
        
        for i, pdf_file in enumerate(pdf_files, 1):
            logging.info(f"Processing file {i}/{len(pdf_files)}: {pdf_file.name}")
            
            try:
                # Extract filename without extension
                original_name = pdf_file.stem
                extension = pdf_file.suffix
                
                # Translate filename
                translated_name = self.translator.translate_filename(original_name)
                
                if translated_name:
                    # Create new filename with extension
                    new_filename = f"{translated_name}{extension}"
                    new_file_path = self.output_folder / new_filename
                    
                    # Handle filename conflicts
                    counter = 1
                    while new_file_path.exists():
                        new_filename = f"{translated_name}_{counter}{extension}"
                        new_file_path = self.output_folder / new_filename
                        counter += 1
                    
                    # Copy file with new name
                    shutil.copy2(pdf_file, new_file_path)
                    
                    results[pdf_file.name] = new_filename
                    successful_translations += 1
                    
                    logging.info(f"Successfully copied: {pdf_file.name} -> {new_filename}")
                else:
                    logging.error(f"Failed to translate filename: {pdf_file.name}")
                    results[pdf_file.name] = "TRANSLATION_FAILED"
                    
            except Exception as e:
                logging.error(f"Error processing file {pdf_file.name}: {e}")
                results[pdf_file.name] = f"ERROR: {str(e)}"
        
        logging.info(f"Processing complete. Successfully translated {successful_translations}/{len(pdf_files)} files.")
        return results


def setup_logging(log_level: str = "INFO") -> None:
    """Setup logging configuration."""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('pdf_translator.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )


def print_results(results: Dict[str, str]) -> None:
    """Print processing results in a formatted way."""
    print("\n" + "="*80)
    print("TRANSLATION RESULTS")
    print("="*80)
    
    successful = 0
    failed = 0
    
    for original, translated in results.items():
        if translated.startswith("ERROR:") or translated == "TRANSLATION_FAILED":
            print(f"❌ {original} -> {translated}")
            failed += 1
        else:
            print(f"✅ {original} -> {translated}")
            successful += 1
    
    print("\n" + "-"*80)
    print(f"Summary: {successful} successful, {failed} failed out of {len(results)} total files")
    print("-"*80)


def main():
    """Main function to run the PDF filename translator."""
    try:
        # Setup logging
        setup_logging()
        
        print("PDF Filename Translator")
        print("="*50)
        
        # Load configuration
        print("Loading configuration...")
        config = ConfigParser()
        print(f"✅ Configuration loaded - Model: {config.get('model')}")
        
        # Initialize AI translator
        print("Initializing AI translator...")
        translator = AITranslator(config)
        print("✅ AI translator initialized")
        
        # Initialize PDF processor
        print("Initializing PDF processor...")
        processor = PDFProcessor()
        processor.set_translator(translator)
        print("✅ PDF processor initialized")
        
        # Scan for PDF files
        print("Scanning for PDF files...")
        pdf_files = processor.scan_pdf_files()
        
        if not pdf_files:
            print("❌ No PDF files found in the 'pdf' folder.")
            return
        
        print(f"✅ Found {len(pdf_files)} PDF files")
        
        # Ask for confirmation
        print(f"\nReady to translate {len(pdf_files)} PDF filenames to Chinese.")
        response = input("Continue? (y/N): ").strip().lower()
        
        if response != 'y':
            print("Operation cancelled.")
            return
        
        # Process files
        print("\nStarting translation process...")
        results = processor.process_files(pdf_files)
        
        # Print results
        print_results(results)
        
        print(f"\nTranslated files saved to: {processor.output_folder}")
        print("Process completed successfully!")
        
    except KeyboardInterrupt:
        print("\n\nOperation cancelled by user.")
    except Exception as e:
        logging.error(f"Fatal error: {e}")
        print(f"\n❌ Fatal error: {e}")
        print("Check pdf_translator.log for detailed error information.")


if __name__ == "__main__":
    main()
