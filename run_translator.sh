#!/bin/bash

echo "PDF Filename Translator"
echo "======================"

echo
echo "Checking Python installation..."
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "ERROR: Python is not installed or not in PATH"
        echo "Please install Python 3.6 or higher"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo "Python found: $($PYTHON_CMD --version)"

echo
echo "Installing/checking dependencies..."
$PYTHON_CMD -m pip install -r requirements.txt

echo
echo "Running configuration test..."
$PYTHON_CMD test_config.py

echo
read -p "Press Enter to run the PDF filename translator..."

echo
echo "Starting PDF filename translator..."
$PYTHON_CMD pdf_filename_translator.py

echo
echo "Process completed."
