@echo off
echo PDF Filename Translator
echo ======================

echo.
echo Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.6 or higher
    pause
    exit /b 1
)

echo Python found!

echo.
echo Installing/checking dependencies...
pip install -r requirements.txt

echo.
echo Running configuration test...
python test_config.py

echo.
echo Press any key to run the PDF filename translator...
pause >nul

echo.
echo Starting PDF filename translator...
python pdf_filename_translator.py

echo.
echo Process completed. Press any key to exit...
pause >nul
