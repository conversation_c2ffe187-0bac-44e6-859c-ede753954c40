# PDF Filename Translator

A Python program that automatically translates PDF filenames from any language to Chinese using AI APIs. The program scans a folder for PDF files, translates their filenames using a configurable AI service, and creates copies with the translated Chinese filenames.

## Features

- 📁 **Automatic PDF Discovery**: Scans the `pdf` folder for all PDF files
- 🤖 **AI-Powered Translation**: Uses OpenAI-compatible APIs for accurate filename translation
- 🔧 **Configurable**: Easy configuration through `config.txt` file
- 📝 **Smart Filename Cleaning**: Ensures translated filenames are valid for file systems
- 🔄 **Duplicate Handling**: Automatically handles filename conflicts
- 📊 **Detailed Logging**: Comprehensive logging and progress reporting
- ⚡ **Rate Limiting**: Built-in rate limiting to respect API limits
- 🛡️ **Error Handling**: Robust error handling with retry mechanisms

## Requirements

- Python 3.6 or higher
- `requests` library
- A valid AI API endpoint (OpenAI-compatible)

## Installation

1. Clone or download this repository
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

## Configuration

Create or update the `config.txt` file with your AI service configuration:

```
base_url:http://*************:3000/v1
api:your-api-key-here
model:gemini-2.0-flash
```

### Configuration Parameters

- **base_url**: The base URL of your AI service (OpenAI-compatible endpoint)
- **api**: Your API key for authentication
- **model**: The AI model to use for translation

## Usage

1. **Prepare your PDF files**: Place all PDF files you want to process in the `pdf` folder

2. **Run the translator**:
   ```bash
   python pdf_filename_translator.py
   ```

3. **Follow the prompts**: The program will:
   - Load configuration from `config.txt`
   - Scan for PDF files in the `pdf` folder
   - Show you how many files were found
   - Ask for confirmation before proceeding
   - Translate each filename to Chinese
   - Create copies with translated names in the `translated_pdfs` folder

## Example

**Before (Original files in `pdf` folder):**
```
pdf/
├── Page01_Chinese nuclear weapons, 2025.pdf
├── Page01_French nuclear weapons, 2025.pdf
├── Page01_Indian nuclear weapons, 2024.pdf
└── Page01_Nuclear arsenals of the world.pdf
```

**After (Translated files in `translated_pdfs` folder):**
```
translated_pdfs/
├── 2025年中国核武器.pdf
├── 2025年法国核武器.pdf
├── 2024年印度核武器.pdf
└── 世界核武库.pdf
```

## Output

The program creates a `translated_pdfs` folder containing:
- Copies of all original PDF files
- Filenames translated to Chinese
- Original files remain unchanged in the `pdf` folder

## Logging

The program generates detailed logs in `pdf_translator.log` including:
- Configuration loading status
- Translation progress
- API call results
- Error messages and debugging information

## Error Handling

The program includes comprehensive error handling for:
- Missing or invalid configuration files
- Network connectivity issues
- API rate limiting and timeouts
- Invalid filenames and file system errors
- Duplicate filename conflicts

## Customization

### Changing Input/Output Folders

You can modify the folder paths by editing the `PDFProcessor` initialization in the `main()` function:

```python
processor = PDFProcessor(pdf_folder="your_input_folder", output_folder="your_output_folder")
```

### Adjusting Rate Limiting

Modify the `min_request_interval` in the `AITranslator` class to change the delay between API calls:

```python
self.min_request_interval = 1.0  # 1 second between requests
```

### Custom Translation Prompts

Edit the `translate_filename()` method in the `AITranslator` class to customize the translation prompt.

## Troubleshooting

### Common Issues

1. **"Configuration file config.txt not found"**
   - Ensure `config.txt` exists in the same directory as the script
   - Check the file format matches the example above

2. **"No PDF files found"**
   - Verify PDF files are in the `pdf` folder
   - Check file extensions (both `.pdf` and `.PDF` are supported)

3. **API Connection Errors**
   - Verify your `base_url` is correct and accessible
   - Check your API key is valid
   - Ensure your network connection is stable

4. **Translation Failures**
   - Check the API service is running and responsive
   - Verify the model name is correct
   - Review logs for specific error messages

### Debug Mode

For detailed debugging information, you can modify the logging level in the `setup_logging()` function:

```python
setup_logging(log_level="DEBUG")
```

## API Compatibility

This program is designed to work with OpenAI-compatible APIs, including:
- OpenAI GPT models
- Local AI services (like the one in your config)
- Other compatible endpoints

The API endpoint should support the `/chat/completions` endpoint with the standard OpenAI format.

## License

This project is provided as-is for educational and personal use.

## Support

For issues or questions:
1. Check the log file (`pdf_translator.log`) for detailed error information
2. Verify your configuration settings
3. Test your API endpoint independently
4. Review the troubleshooting section above
