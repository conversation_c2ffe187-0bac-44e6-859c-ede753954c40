#!/usr/bin/env python3
"""
Configuration and API Test Script

This script tests the configuration file and API connectivity
before running the main PDF filename translator.

Author: AI Assistant
Date: 2025-07-31
"""

import sys
import os
from pdf_filename_translator import Confi<PERSON><PERSON><PERSON><PERSON>, AITranslator, setup_logging


def test_configuration():
    """Test configuration file loading."""
    print("Testing configuration file...")
    
    try:
        config = ConfigParser()
        print("✅ Configuration file loaded successfully")
        
        # Display configuration (hide API key for security)
        config_data = config.get_all()
        for key, value in config_data.items():
            if key.lower() in ['api', 'api_key', 'key']:
                display_value = f"{value[:8]}..." if len(value) > 8 else "***"
            else:
                display_value = value
            print(f"   {key}: {display_value}")
        
        return config
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return None


def test_api_connectivity(config):
    """Test API connectivity and translation."""
    print("\nTesting API connectivity...")
    
    try:
        translator = AITranslator(config)
        print("✅ AI Translator initialized")
        
        # Test translation with a simple filename
        test_filename = "Nuclear weapons report 2024"
        print(f"Testing translation of: '{test_filename}'")
        
        translated = translator.translate_filename(test_filename)
        
        if translated:
            print(f"✅ Translation successful: '{translated}'")
            return True
        else:
            print("❌ Translation failed - no result returned")
            return False
            
    except Exception as e:
        print(f"❌ API test failed: {e}")
        return False


def test_pdf_folder():
    """Test PDF folder existence and contents."""
    print("\nTesting PDF folder...")
    
    pdf_folder = "pdf"
    
    if not os.path.exists(pdf_folder):
        print(f"❌ PDF folder '{pdf_folder}' does not exist")
        return False
    
    print(f"✅ PDF folder '{pdf_folder}' exists")
    
    # Count PDF files
    pdf_files = []
    for file in os.listdir(pdf_folder):
        if file.lower().endswith('.pdf'):
            pdf_files.append(file)
    
    print(f"   Found {len(pdf_files)} PDF files")
    
    if len(pdf_files) > 0:
        print("   Sample files:")
        for i, file in enumerate(pdf_files[:3]):  # Show first 3 files
            print(f"     {i+1}. {file}")
        if len(pdf_files) > 3:
            print(f"     ... and {len(pdf_files) - 3} more files")
    
    return len(pdf_files) > 0


def test_output_folder():
    """Test output folder creation."""
    print("\nTesting output folder...")
    
    output_folder = "translated_pdfs"
    
    try:
        os.makedirs(output_folder, exist_ok=True)
        print(f"✅ Output folder '{output_folder}' is ready")
        return True
    except Exception as e:
        print(f"❌ Failed to create output folder: {e}")
        return False


def main():
    """Run all tests."""
    print("PDF Filename Translator - Configuration Test")
    print("=" * 50)
    
    # Setup logging for tests
    setup_logging(log_level="WARNING")  # Reduce log noise during testing
    
    # Run tests
    tests_passed = 0
    total_tests = 4
    
    # Test 1: Configuration
    config = test_configuration()
    if config:
        tests_passed += 1
    
    # Test 2: API connectivity (only if config is valid)
    if config:
        if test_api_connectivity(config):
            tests_passed += 1
    else:
        print("\nSkipping API test due to configuration failure")
    
    # Test 3: PDF folder
    if test_pdf_folder():
        tests_passed += 1
    
    # Test 4: Output folder
    if test_output_folder():
        tests_passed += 1
    
    # Summary
    print("\n" + "=" * 50)
    print(f"Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("✅ All tests passed! You're ready to run the PDF translator.")
    else:
        print("❌ Some tests failed. Please fix the issues before running the translator.")
        print("\nCommon solutions:")
        print("- Ensure config.txt exists with valid API configuration")
        print("- Check that the 'pdf' folder exists and contains PDF files")
        print("- Verify your API endpoint is accessible")
        print("- Make sure you have write permissions in the current directory")
    
    print("=" * 50)


if __name__ == "__main__":
    main()
