#!/usr/bin/env python3
"""
Quick script to check PDF file count and verify the fix
"""

from pathlib import Path

def check_pdf_files():
    pdf_folder = Path("pdf")
    
    if not pdf_folder.exists():
        print("PDF folder not found!")
        return
    
    # Method 1: Using glob patterns (old way - causes duplicates)
    pdf_files_glob = list(pdf_folder.glob("*.pdf"))
    pdf_files_glob.extend(pdf_folder.glob("*.PDF"))
    
    # Method 2: Using iterdir (new way - no duplicates)
    pdf_files_iterdir = []
    for file_path in pdf_folder.iterdir():
        if file_path.is_file() and file_path.suffix.lower() == '.pdf':
            pdf_files_iterdir.append(file_path)
    
    print(f"Method 1 (glob - old): {len(pdf_files_glob)} files")
    print(f"Method 2 (iterdir - new): {len(pdf_files_iterdir)} files")
    
    # Check for duplicates in method 1
    unique_files_glob = list(set(pdf_files_glob))
    print(f"Method 1 unique files: {len(unique_files_glob)} files")
    
    if len(pdf_files_glob) != len(unique_files_glob):
        print("⚠️  Method 1 has duplicates!")
        duplicates = len(pdf_files_glob) - len(unique_files_glob)
        print(f"   Number of duplicates: {duplicates}")
    else:
        print("✅ Method 1 has no duplicates")
    
    print(f"\n✅ Correct file count: {len(pdf_files_iterdir)}")

if __name__ == "__main__":
    check_pdf_files()
